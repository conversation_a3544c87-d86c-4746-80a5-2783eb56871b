import {
  Category,
  Client,
  ExpoPushToken,
  Notification,
  OTP,
  Profile,
  Vendor,
  Brand,
  Order,
  Product,
  Review,
} from "../database";
import { errorResponse, successResponse } from "../utils";
import bcrypt from "bcryptjs";

export const getProfile = async (req: any, res: any) => {
  try {
    const { id, device_id } = req.params;
    if (!id) {
      return errorResponse(res, "backend.invalid_service_id", 404);
    }
    const expoPushToken = await ExpoPushToken.findOne({
      user_id: id,
      device_id: device_id,
    }).lean();
    if (!expoPushToken) {
      return errorResponse(res, "backend.expo_push_token_not_found", 404);
    }
    return res.status(200).json({
      ok: true,
      status: "success",
      message: "backend.service_retrieved_successfully",
      data: expoPushToken,
    });
  } catch (error) {
    console.error("Error retrieving service:", error);
    return errorResponse(res, "backend.get_profile_failed", 404);
  }
};

export const getUserProfile = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    if (!id) {
      return errorResponse(res, "backend.invalid_service_id", 404);
    }
    const profile = await Profile.findById(id).lean();
    if (!profile) {
      return errorResponse(res, "backend.profile_not_found", 404);
    }
    return res.status(200).json({
      ok: true,
      status: "success",
      message: "backend.service_retrieved_successfully",
      data: profile,
    });
  } catch (error) {
    console.error("backend.Error retrieving service:", error);
    return errorResponse(res, "backend.get_profile_failed", 404);
  }
};

export const updateProfile = async (req: any, res: any) => {
  try {
    const { userId, device_id } = req.params;
    const updateData = req.body;

    const {
      notification,
      emailNotification,
      bookingUpdate,
      newMessage,
      marketing,
    } = updateData;
    console.log(
      notification,
      emailNotification,
      bookingUpdate,
      newMessage,
      marketing
    );
    const result = await ExpoPushToken.updateOne(
      { user_id: userId, device_id: device_id },
      { notification, newMessage },
      { runValidators: true }
    );
    const result2 = await ExpoPushToken.updateMany(
      { user_id: userId },
      { emailNotification, bookingUpdate, marketing },
      { runValidators: true }
    );
    if (result.matchedCount === 0 || result2.matchedCount === 0) {
      return errorResponse(res, "backend.no_expo_push_tokens_found", 404);
    }
    const updatedTokens = await ExpoPushToken.findOne({
      user_id: userId,
      device_id: device_id,
    }).lean();

    return res.status(200).json({
      ok: true,
      status: "success",
      message: "backend.expo_push_tokens_updated",
      data: updatedTokens,
    });
  } catch (error) {
    console.error("Error updating profile:", error);
    return errorResponse(res, "backend.error_updating_expo_push_tokens", 404);
  }
};

// Helper function to delete client account and related data
const deleteClientAccount = async (
  userId: string,
  profileId: string,
  profile: any
) => {
  const client = await Client.findById(userId);
  if (!client) {
    throw new Error("backend.client_not_found");
  }

  // Get client's orders to provide statistics
  const clientOrders = await Order.find({ client: userId });
  const orderStats = {
    totalOrders: clientOrders.length,
    pendingOrders: clientOrders.filter(
      (order) => order.orderStatus === "Pending"
    ).length,
    deliveredOrders: clientOrders.filter(
      (order) => order.orderStatus === "Delivered"
    ).length,
    cancelledOrders: clientOrders.filter(
      (order) => order.orderStatus === "Cancelled"
    ).length,
  };

  // Cancel all pending orders
  await Order.updateMany(
    { client: userId, orderStatus: "Pending" },
    { orderStatus: "Cancelled" }
  );

  // Clean up related data (preserve reviews for business continuity)
  await Promise.all([
    ExpoPushToken.deleteMany({ user_id: profileId }),
    OTP.deleteMany({ email: profile.email }),
    Notification.deleteMany({ profile: profileId }),
    // Note: Reviews are preserved to maintain review history and ratings
  ]);

  // Delete client and profile
  await Promise.all([
    Client.deleteOne({ _id: userId }),
    Profile.deleteOne({ _id: profileId }),
  ]);

  console.log(`Client account deleted. Order statistics:`, orderStats);
};

// Helper function to delete vendor account and related data
const deleteVendorAccount = async (
  userId: string,
  profileId: string,
  profile: any
) => {
  const vendor = await Vendor.findById(userId).populate("brands");
  if (!vendor) {
    throw new Error("backend.vendor_not_found");
  }

  // Get all brands owned by this vendor
  const brandIds = vendor.brands.map((brand: any) => brand._id);

  if (brandIds.length > 0) {
    // Check for existing orders across all brands
    const existingOrders = await Order.find({
      brand: { $in: brandIds },
      orderStatus: { $in: ["Pending", "Accepted"] },
    });

    // If there are active orders, prevent deletion
    if (existingOrders.length > 0) {
      const pendingCount = existingOrders.filter(
        (order) => order.orderStatus === "Pending"
      ).length;
      const acceptedCount = existingOrders.filter(
        (order) => order.orderStatus === "Accepted"
      ).length;

      return {
        success: false,
        message: `backend.cannot_delete_vendor_with_active_orders`,
        statusCode: 400,
        data: {
          pendingOrders: pendingCount,
          acceptedOrders: acceptedCount,
          totalActiveOrders: existingOrders.length,
        },
      };
    }

    // Get order statistics for logging
    const allOrders = await Order.find({ brand: { $in: brandIds } });
    const orderStats = {
      totalOrders: allOrders.length,
      deliveredOrders: allOrders.filter(
        (order) => order.orderStatus === "Delivered"
      ).length,
      cancelledOrders: allOrders.filter(
        (order) => order.orderStatus === "Cancelled"
      ).length,
    };

    // Get all product IDs associated with these brands
    const products = await Product.find({ brand: { $in: brandIds } }).select(
      "_id"
    );
    const productIds = products.map((product) => product._id);

    // Remove brand IDs and their product IDs from all clients' favorites
    await Client.updateMany(
      {
        $or: [
          { favorite_brands: { $in: brandIds } },
          { favorite_products: { $in: productIds } },
        ],
      },
      {
        $pull: {
          favorite_brands: { $in: brandIds },
          favorite_products: { $in: productIds },
        },
      }
    );

    // Delete all products associated with the brands
    await Product.deleteMany({ brand: { $in: brandIds } });

    // Delete all reviews for the brands
    await Review.deleteMany({ brand: { $in: brandIds } });

    // Delete all brands
    await Brand.deleteMany({ _id: { $in: brandIds } });

    console.log(
      `Vendor account deleted. Brands: ${brandIds.length}, Products: ${productIds.length}, Order statistics:`,
      orderStats
    );
    console.log(`Removed brand and product favorites from clients`);
  }

  // Clean up related data
  await Promise.all([
    ExpoPushToken.deleteMany({ user_id: profileId }),
    OTP.deleteMany({ email: profile.email }),
    Notification.deleteMany({ profile: profileId }),
  ]);

  // Delete vendor and profile
  await Promise.all([
    Vendor.deleteOne({ _id: userId }),
    Profile.deleteOne({ _id: profileId }),
  ]);

  return { success: true };
};

export const deleteAccount = async (req: any, res: any) => {
  try {
    const { type } = req.body;
    const userId = req.user?.user_id;
    const profileId = req.user?.id;

    // Validate type parameter
    if (!type || !["client", "vendor"].includes(type)) {
      return errorResponse(res, "backend.invalid_account_type", 400);
    }

    // Get profile first
    const profile = await Profile.findById(profileId);
    if (!profile) {
      return errorResponse(res, "backend.profile_not_found", 404);
    }

    // Verify profile type matches requested deletion type
    if (profile.type !== type) {
      return errorResponse(res, "backend.account_type_mismatch", 400);
    }

    if (type === "client") {
      await deleteClientAccount(userId, profileId, profile);
    } else if (type === "vendor") {
      const result = await deleteVendorAccount(userId, profileId, profile);
      if (!result.success) {
        return res.status(result.statusCode).json({
          ok: false,
          status: "error",
          message: result.message,
          data: result.data,
        });
      }
    }

    return successResponse(res, "backend.account_deleted_successfully");
  } catch (error: any) {
    console.error("Delete account error:", error);
    return errorResponse(res, error.message || "backend.server_error", 500);
  }
};
export const addToken = async (req: any, res: any) => {
  try {
    const { expoPushToken, type, device_id, device_type, status } = req.body;
    const validTypes = ["client", "vendor"];
    if (!validTypes.includes(type)) {
      return errorResponse(
        res,
        "backend.invalid_type_must_be_hotel_client_or_admin",
        400
      );
    }
    let user: any = req.user;

    const existingToken = await ExpoPushToken.findOne({
      device_id: device_id,
      user_id: user.id,
    });

    if (existingToken) {
      existingToken.expoPushToken = expoPushToken;
      existingToken.user_id = user.id;
      existingToken.type = type;
      existingToken.device_id = device_id;
      existingToken.device_type = device_type;
      existingToken.notification = status;
      existingToken.newMessage = status;
      await existingToken.save();
      return successResponse(res, "backend.expo_push_token_updated", {
        token: existingToken,
      });
    }
    const newExpoPushToken = new ExpoPushToken({
      expoPushToken,
      type,
      active: true,
      device_id,
      device_type,
      user_id: user.id,
      notification: status,
      emailNotification: true,
      bookingUpdate: true,
      newMessage: status,
      marketing: true,
    });
    await newExpoPushToken.save();
    return successResponse(res, "backend.expo_push_token_added", {
      token: newExpoPushToken,
    });
  } catch (error: any) {
    console.log(error);
    return errorResponse(res, error.message || "backend.server_error", 500);
  }
};

export const changePassword = async (req: any, res: any) => {
  try {
    const userId = req.user?.id;
    const { oldPassword, newPassword, confirmNewPassword } = req.body;

    if (newPassword !== confirmNewPassword) {
      return errorResponse(
        res,
        "backend.new_password_and_confirm_password_do_not_match",
        400
      );
    }

    if (!oldPassword) {
      return errorResponse(res, "backend.old_password_is_required", 400);
    }

    const profile = await Profile.findById(userId);
    if (!profile) {
      return errorResponse(res, "backend.user_not_found", 404);
    }

    const isPasswordValid = await bcrypt.compare(oldPassword, profile.password);
    if (!isPasswordValid) {
      return errorResponse(res, "backend.old_password_is_incorrect", 401);
    }

    const isSamePassword = await bcrypt.compare(newPassword, profile.password);
    if (isSamePassword) {
      return errorResponse(
        res,
        "backend.new_password_must_be_different_from_old_password",
        400
      );
    }
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    profile.password = hashedPassword;
    profile.loginHistory.unshift({
      action: "password-change",
      date: new Date().toISOString(),
    });
    await profile.save();
    return successResponse(res, "password_changed");
  } catch (error) {
    console.error("Error changing password:", error);
    return errorResponse(res, "backend.failed_to_change_password", 500);
  }
};

export const getCategoriesList = async (req: any, res: any) => {
  try {
    const result = await Category.find(); // Added 'await' here
    return successResponse(res, "backend.categories_found", result);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return errorResponse(res, "backend.failed_to_fetch_categories", 500);
  }
};
