// cron/syncTypesense.ts
import mongoose from "mongoose";
import { typesense } from "../../services/TypeSenseClient";
import Product from "../models/product/product";
import Brand from "../models/brand/brand";

export const syncTypeSense = async () => {
  try {
    // Check if database is connected
    if (mongoose.connection.readyState !== 1) {
      console.warn("⚠️ Database not connected, skipping TypeSense sync");
      return;
    }

    console.log("🔍 Starting TypeSense full sync (delete all + re-sync)...");

    // Delete existing collections if they exist
    try {
      const collections = await typesense.collections().retrieve();
      const collectionNames = collections.map((col: any) => col.name);

      if (collectionNames.includes("products")) {
        console.log("🗑️ Deleting existing products collection...");
        await typesense.collections("products").delete();
      }

      if (collectionNames.includes("brands")) {
        console.log("🗑️ Deleting existing brands collection...");
        await typesense.collections("brands").delete();
      }
    } catch (deleteError) {
      console.log("ℹ️ Collections don't exist or already deleted");
    }

    // Create fresh collections
    console.log("🆕 Creating fresh products collection...");
    await typesense.collections().create({
      name: "products",
      fields: [
        { name: "id", type: "string" },
        { name: "type", type: "string" },
        { name: "name", type: "string" },
        { name: "description", type: "string" },
        { name: "category", type: "string", facet: true },
        { name: "price", type: "float" },
        { name: "brand", type: "string", facet: true },
        { name: "brandId", type: "string" },
        { name: "image", type: "string" },
      ],
    });

    console.log("🆕 Creating fresh brands collection...");
    await typesense.collections().create({
      name: "brands",
      fields: [
        { name: "id", type: "string" },
        { name: "type", type: "string" },
        { name: "name", type: "string" },
        { name: "description", type: "string" },
        { name: "image", type: "string" },
        { name: "productCount", type: "int32" },
      ],
    });

    // Sync products with populated brand information
    console.log("📦 Fetching and syncing products...");
    const products = await Product.find()
      .populate("brand", "name")
      .populate("category", "name")
      .lean();

    const productDocs = products.map((p: any) => ({
      id: p._id.toString(),
      type: "product",
      name: p.name,
      description: p.description || "",
      category: p.category?.name || "",
      price: p.price,
      brand: p.brand?.name || "",
      brandId: p.brand?._id?.toString() || "",
      image: p.images && p.images.length > 0 ? p.images[0] : "",
    }));

    if (productDocs.length > 0) {
      await typesense
        .collections("products")
        .documents()
        .import(productDocs, { action: "create" }); // Using 'create' instead of 'upsert' for fresh data
      console.log(`✅ Imported ${productDocs.length} products`);
    } else {
      console.log("ℹ️ No products found to import");
    }

    // Sync brands with product count
    console.log("🏷️ Fetching and syncing brands...");
    const brands = await Brand.find().lean();
    const brandDocs = await Promise.all(
      brands.map(async (b: any) => {
        const productCount = await Product.countDocuments({ brand: b._id });
        return {
          id: b._id.toString(),
          type: "brand",
          name: b.name,
          description: b.description || "",
          image: b.logo || "",
          productCount,
        };
      })
    );

    if (brandDocs.length > 0) {
      await typesense
        .collections("brands")
        .documents()
        .import(brandDocs, { action: "create" }); // Using 'create' instead of 'upsert' for fresh data
      console.log(`✅ Imported ${brandDocs.length} brands`);
    } else {
      console.log("ℹ️ No brands found to import");
    }

    console.log("🎉 TypeSense full sync completed successfully!");
    console.log(
      `📊 Final stats: ${productDocs.length} products, ${brandDocs.length} brands`
    );
  } catch (err) {
    console.error("❌ Sync error:", err);
    throw err; // Re-throw to handle upstream if needed
  }
};
