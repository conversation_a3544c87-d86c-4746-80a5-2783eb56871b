version: "3.8"

services:
  auth:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    ports:
      - "9081:9081"
    restart: always
    depends_on:
      rabbitmq:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=9081
      - JWT_SECRET=9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08
      - MONGO_URI=mongodb+srv://myAtlasDBUser:<EMAIL>/locasa-users?retryWrites=true&w=majority
      - MESSAGE_BROKER_URL=amqp://ukps:jD8tXx6Gxw9Xb9LpQvR2m3AaLqKcNvYzAESDW@rabbitmq:5672

  notification:
    build:
      context: ./notification-service
      dockerfile: Dockerfile
    ports:
      - "9082:9082"
    depends_on:
      rabbitmq:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=9082
      - MONGO_URI=mongodb+srv://myAtlasDBUser:<EMAIL>/locasa-users?retryWrites=true&w=majority
      - MESSAGE_BROKER_URL=amqp://ukps:jD8tXx6Gxw9Xb9LpQvR2m3AaLqKcNvYzAESDW@rabbitmq:5672
      - SMTP_HOST=smtp.sendgrid.net
      - SMTP_PORT=587
      - SMTP_USER=apikey
      - SMTP_PASS=*********************************************************************
      - EMAIL_FROM=<EMAIL>
      - accountSid=**********************************
      - authToken=44ed0c29dc5a5b71a0453745540615fc
      - EXPO_ACCESS_TOKEN=vW_cXnJtjyk5E7DLH6LrWcs3GjYllorD83cQFot2

  locasa:
    build:
      context: ./locasa-service
      dockerfile: Dockerfile
    ports:
      - "9093:9093"
    depends_on:
      rabbitmq:
        condition: service_healthy
      typesense:
        condition: service_started
    environment:
      - NODE_ENV=production
      - PORT=9093
      - JWT_SECRET=9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08
      - MONGO_URI=mongodb+srv://myAtlasDBUser:<EMAIL>/locasa-users?retryWrites=true&w=majority
      - MESSAGE_BROKER_URL=amqp://ukps:jD8tXx6Gxw9Xb9LpQvR2m3AaLqKcNvYzAESDW@rabbitmq:5672
      - AWS_ACCESS_KEY_ID=********************
      - AWS_SECRET_ACCESS_KEY=X+si+XXyaHgQOzQF6eEn3Jrkahiuwct2uss5Orx1
      - AWS_REGION=eu-central-1
      - AWS_BUCKET_NAME=locasa-bucket
      - TYPESENSE_API_KEY=*****************************+fJ50QzlvboP14Fd3H9sKs=
      - TYPESENSE_HOST=typesense
      - TYPESENSE_PORT=8108
      - TYPESENSE_PROTOCOL=http

  nginx:
    build:
      context: ./nginx-service
      dockerfile: Dockerfile
    depends_on:
      - auth
      - notification
      - locasa
    ports:
      - "86:86"

  typesense:
    image: typesense/typesense:0.24.1
    container_name: typesense
    ports:
      - "8108:8108"
    volumes:
      - typesense-data:/data
    command: >
      --data-dir /data
      --api-key=*****************************+fJ50QzlvboP14Fd3H9sKs=
      --listen-port 8108
    restart: always

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=ukps
      - RABBITMQ_DEFAULT_PASS=jD8tXx6Gxw9Xb9LpQvR2m3AaLqKcNvYzAESDW
    restart: always
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  typesense-data:
